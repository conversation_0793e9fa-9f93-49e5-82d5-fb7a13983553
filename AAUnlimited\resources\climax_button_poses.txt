// Climax Button config 
// (Configured for the More Cumming Positions v262)
// 
// ------------- READ before making changes ---------------------
// Pose redirection by default is based on their categories (id_category):
// 'touches' (1) 	=> 'climax touches' (6)
// 'service' (2) 	=> 'climax service' (7)
// 'masturbate' (3) => 'climax touches' (6) (continue after category 1)
// 'vaginal sex (4)'=> 'climax vaginal sex' (8)
// 'anal sex' (5) 	=> 'climax anal sex' (9)
// But some of normal versions of Poses can have a 0, 2 or 3
// 		climax versions. In this case, the default order will shift
//		and must be corrected in this config (see below, how to do this).
// Starting from 6 category (climax categories), 
// 		the pose redirection goes to the same pose and category
// 		and can't changed in this config (It makes no sense).
// For example, by default pose 10 in 1st category 
// 		redirects her climax version on 10 pose in 6 category.
// In the same time pose 10 in 8th category redirects her 
//		climax version on 10 pose in 8th category.
//
// How to configure current poses pack:
// 1) Clear/Comment all lines, starting with
// 		[FIX_HETERO] ...
// 		[FIX_HOMO] ...
// 		[HETERO_LINK] ...
// 		[HETERO_SHIFT] ...
// 		[HOMO_LINK] ...
// 		[HOMO_SHIFT] ...
// 2) Set `Debug` param from `false` to `true` value
// Now, you can see all necesseary info for working.
// 
// 3) Fix poses, that are not registered.
// For this at start Set `VerifyRegister` param from `false` to `true` value
// This forces only show info about pose, when pressed 'Start Climax' (for better visual debugging).
// After that, start the H-scene, select poses one by one and click 'Start Climax'
// If you see in the log after Current pose id the message 'Current pose is unregistered',
// then the pose must be registered manually in this config in 'Fix register poses id' section.
// Use, for example `[FIX_HETERO] 15` - for the fix Hetero pose 
// with id 15 (see your id in logs `Current pose id: ...` after pressing 'Start Climax').
// 
// 4) Make sure all positions are registered in the previous step.
//
// 5) Verify attaching status between normal and climax poses.
// For this Set `VerifyRegister` param from `true` to `false` value.
// After that, start the H-scene, select poses one by one and click 'Start Climax'
// In this cases, every current position must redirecting to his climax version.
// If you have noticed Shifts, these Shifts can be caused by 2 factors:
// Some of Normal pose versions doesn't have a climax versions at all, or have more, than 1.
// In this case use `_LINK` command for problem poses 
// in `Hetero and homo link fixes` section of this config. Example:
// 
// [HETERO_LINK] 	{4, 1} => {8, 12} {8, 13} {8, 15}   // 3 climax ver. for 1st button in 4th category
// [HOMO_LINK] 		{5, 14} => {9, 13} {} {}			// 1 climax ver. for 14th button in 5th category
// 
// Also if some pose has a number of climax poses that are different from one,
// there is often need to move the pointer among the climax poses. Example:
// [HETERO_LINK] 	{4, 1} => {8, 13} {8, 14} {}	// Set 2 climax poses for 1 normal hetero pose
// [HETERO_SHIFT]	+1								// shift pointer by +1 as it was occupied by 2 poses.
// But for shift you can also use munis symbol for negative shift.
// [HOMO_SHIFT]	-2
//
// If all verifications complete... Have fun!)
// (Do not forget to set `Debug` param from `true` to `false` value =) )
// --------------------------------------------------------------

Debug=false
VerifyRegister=false

// Fix register poses id
//[FIX_HETERO]	33
//[FIX_HOMO]	180


// Hetero and homo link fixes START ----------------------
// (order this lines is important!)

[HETERO_LINK] 	{2, 14} => {7, 12} {} {}
[HETERO_LINK] 	{2, 15} => {7, 3} {} {}
[HETERO_LINK] 	{2, 16} => {7, 18} {} {}
[HETERO_LINK] 	{4, 1} => {8, 7} {} {}
[HETERO_SHIFT]	+6

// {4, 2} => {8, 8} ... {4, 4} => {8, 10}

[HETERO_LINK] 	{4, 5} => {8, 1} {} {}
[HETERO_SHIFT]	-1

// {4, 6} => {8, 11} ... {4, 16} => {8, 21}

[HETERO_LINK] 	{4, 17} => {8, 4} {} {}
[HETERO_LINK] 	{4, 18} => {8, 6} {} {}
[HETERO_LINK] 	{4, 19} => {8, 3} {} {}
[HETERO_LINK] 	{5, 1} => {9, 4} {} {}
[HETERO_SHIFT]	+3

// {5, 2} => {9, 5} ... {5, 4} => {9, 7}

[HETERO_LINK] 	{5, 5} => {9, 2} {} {}
[HETERO_SHIFT]	-1

// {5, 6} => {9, 8} ... {5, 8} => {9, 10}

[HETERO_LINK] 	{5, 9} => {9, 4} {} {}
[HETERO_LINK] 	{5, 10} => {9, 3} {} {}
[HETERO_LINK] 	{5, 11} => {9, 7} {} {}
[HETERO_LINK] 	{5, 12} => {9, 4} {} {}


[HOMO_LINK] 	{4, 1} => {8, 6} {} {}
[HOMO_SHIFT]	+5

// {4, 2} => {8, 7} ... {4, 4} => {8, 9}

[HOMO_LINK] 	{4, 5} => {8, 12} {} {}
[HOMO_SHIFT]	-1

// {4, 6} => {8, 10} ... {4, 16} => {8, 20}

[HOMO_LINK] 	{4, 17} => {8, 3} {} {}
[HOMO_LINK] 	{4, 18} => {8, 5} {} {}
[HOMO_LINK] 	{4, 19} => {8, 2} {} {}
[HOMO_LINK] 	{5, 1} => {9, 4} {} {}
[HOMO_SHIFT]	+3

// {5, 2} => {9, 5} ... {5, 4} => {9, 7}

[HOMO_LINK] 	{5, 5} => {9, 2} {} {}
[HOMO_SHIFT]	-1

// {5, 6} => {9, 8} ... {5, 7} => {9, 9}

[HOMO_LINK] 	{5, 8} => {9, 8} {} {}
[HOMO_LINK] 	{5, 9} => {9, 4} {} {}
[HOMO_LINK] 	{5, 10} => {9, 3} {} {}
[HOMO_LINK] 	{5, 11} => {9, 7} {} {}
[HOMO_LINK] 	{5, 12} => {9, 4} {} {}

// Example
//[HETERO_LINK] 	{4, 17} => {8, 17} {8, 18} {}
//[HETERO_SHIFT]	+1
//[HOMO_LINK]		{3, 35} => {6, 23} {6, 24} {}
//[HOMO_SHIFT]	-2

// Hetero and homo link fixes END -----------------------
