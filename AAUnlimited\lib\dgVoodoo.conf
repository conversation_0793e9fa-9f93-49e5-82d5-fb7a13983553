;==========================================================================
; === Text based config file for dgVoodoo2
; === Use this file if you are a game modder/hacker or an experted user and
;     want to modify some advanced properties not available via the CPL.
;==========================================================================

Version                              = 0x270

;--------------------------------------------------------------------------

[General]

;       OutputAPI: "d3d11warp", "d3d11_fl10_0", "d3d11_fl10_1", "d3d11_fl11_0",
;                  "d3d12_fl11_0", "d3d12_fl12_0", "bestavailable"
;        Adapters: "all", or the ordinal of the adapter (1, ...)
;FullScreenOutput: "default", or the ordinal of the output on the adapter (1, ...)
;     ScalingMode: "unspecified", "centered", "stretched", "centered_ar", "stretched_ar",
;                  "stretched_4_3", "stretched_4_3_crt", "stretched_4_3_c64"

OutputAPI                            = bestavailable
Adapters                             = all
FullScreenOutput                     = default
FullScreenMode                       = true
ScalingMode                          = unspecified
ProgressiveScanlineOrder             = false
EnumerateRefreshRates                = false

Brightness                           = 100
Color                                = 100
Contrast                             = 100
InheritColorProfileInFullScreenMode  = false

KeepWindowAspectRatio                = true
CaptureMouse                         = true
CenterAppWindow                      = false

;--------------------------------------------------------------------------

[GeneralExt]

;      DesktopResolution: Desktop (native) resolution can be forced for dgVoodoo's internal calculations.
;                         Useful for rare applications that pre-set the desktop to other than the native
;                         resolution before dgVoodoo gets in action. Only the compact format can be used here,
;                         and applies to all outputs of the desktop.
;        DesktopBitDepth: You can define what screen bit depth should be reported through dgVoodoo
;                         (8, 16, 32)
;           DeframerSize: When resolution is forced to other than the app default then
;                         a black frame is drawn around the output image coming from a wrapped API
;                         to remove scaling artifacts -
;                         frame thickness can be defined in pixels (max 16, 0 = disable)
;       ImageScaleFactor: Integer factor for scaling the output image coming from a wrapped API
;                         Always done by nearest point filtering, independent on scaling mode
;                         (0 = max available)
;                         Separate factors can be defined for horizontal and vertical scaling
;                         by subproperties, e.g.
;                         ImageScaleFactor = x:3, y:2
;             DisplayROI: Display region of interest
;                         If scaling is done by the dgVoodoo then you can define a subrect of the
;                         output image, coming from a wrapped API, to be displayed. The defined subrect
;                         is mapped to the display output according to the current scaling mode
;                         It can be useful for applications rendering a widescreen subimage into a 4:3
;                         resolution; in this case you can scale up that subimage to (nearly) fill the
;                         whole screen on a widescreen monitor.
;                         DisplayROI empty value means the whole image.
;                         DisplayROI value can be a proportion in form of %d_%d or a pixel size (%d|%d)
;                         Pos subproperty is not mandatory and can be 'centered' or a pixel position (%d|%d)
;                         Examples: DisplayROI = 16_9, pos:centered
;                                   DisplayROI = (320|200), pos:(10|10)
;             Resampling: When scaling is done by the dgVoodoo for the given scaling mode,
;                         you can choose which filter is to be used for resampling the output image
;                         Available filters are: "pointsampled", "bilinear", "bicubic", "lanczos-2", "lanczos-3"
;              FreeMouse: If true then physical mouse is free to move inside the game window 
;                         when using emulated scaling and/or application and forced resolution
;                         differs; can be useful when a game relies on the physical window size
;     WindowedAttributes: You can define attributes for forced windowed appearance (separated by commas):
;                          "borderless"     - forces the app window not have any border
;                          "alwaysontop"    - forces the app window into the top-most band
;                          "fullscreensize" - forces the app window to be full screen size with image scaling inside
;            Environment: Software environment in which dgVoodoo is running: can be left unspecified (native)
;                         or can be set to 'DosBox' or 'QEmu'.
;       EnableGDIHooking: If enabled then dgVoodoo hooks GDI to be able to render graphical contents
;                         (like movie playback through the ancient Windows Multimedia AVI player library)
;                         rendered through GDI - experimental feature, for the time being it's implemented
;                         only for DX emulation

DesktopResolution                    = 
DesktopBitDepth                      = 
DeframerSize                         = 1
ImageScaleFactor                     = 1
DisplayROI                           = 
Resampling                           = bilinear
FreeMouse                            = false
WindowedAttributes                   = 
Environment                          = 
EnableGDIHooking                     = false

;--------------------------------------------------------------------------

[Glide]

;  VideoCard:      "voodoo_graphics", "voodoo_rush", "voodoo_2", "voodoo_banshee", "other_greater"
; OnboardRAM:      in MBs
; MemorySizeOfTMU: in kBs
;    TMUFiltering: "appdriven", "pointsampled", "bilinear"
;
; Resolution: either "unforced", "max", "max_isf", "max_fhd", "max_fhd_isf", "max_qhd", "max_qhd_isf", "%d x"
;             or subproperties: h: horizontal, v: vertical
;             + optional subproperty refrate: refresh rate in Hz
;             e.g. Resolution = max, refrate:60
;                  Resolution = 2x, refrate:59
;                  Resolution = h:1280, v:1024, refrate:75
;             or just use the compact form like "1024x768@60" or "512x384"
;
;Antialiasing: "off", "appdriven", "2x", "4x", "8x", "16x" (your GPU must support the chosen one)

VideoCard                           = voodoo_2
OnboardRAM                          = 8
MemorySizeOfTMU                     = 4096
NumberOfTMUs                        = 2
TMUFiltering                        = appdriven
DisableMipmapping                   = false
Resolution                          = unforced
Antialiasing                        = appdriven

EnableGlideGammaRamp                = true
ForceVerticalSync                   = true
ForceEmulatingTruePCIAccess         = false
16BitDepthBuffer                    = false
3DfxWatermark                       = true
3DfxSplashScreen                    = false
PointcastPalette                    = false
EnableInactiveAppState              = false


;--------------------------------------------------------------------------

[GlideExt]

;               DitheringEffect: "pure32bit", "dither2x2", "dither4x4"
;                     Dithering: "disabled", "appdriven", "forcealways"
;  DitherOrderedMatrixSizeScale: integer scale value for dither matrix size
;                                1 = normal, 2 = double size, etc.
;                                0 = automatic (the aim is to have some retro feel&look)

DitheringEffect                     = pure32bit
Dithering                           = forcealways
DitherOrderedMatrixSizeScale        = 0

;--------------------------------------------------------------------------

[DirectX]

;  VideoCard: "svga", "internal3D", "geforce_ti_4800", "ati_radeon_8500",
;             "matrox_parhelia-512", "geforce_fx_5700_ultra"
;       VRAM: in MBs (default) or in GBs (e.g. VRAM = 2GB)
;  Filtering: "appdriven", "pointsampled", "bilinear", "linearmip", "trilinear"
;             or the integer value of an anisotropic filtering level (1-16)

DisableAndPassThru                  = false

VideoCard                           = internal3D
VRAM                                = 4096
Filtering                           = appdriven
DisableMipmapping                   = true
Resolution                          = unforced, refrate:144
Antialiasing                        = appdriven

AppControlledScreenMode             = true
DisableAltEnterToToggleScreenMode   = true

BilinearBlitStretch                 = true
PhongShadingWhenPossible            = true
ForceVerticalSync                   = false
dgVoodooWatermark                   = false
FastVideoMemoryAccess               = true

;--------------------------------------------------------------------------

[DirectXExt]

;                 AdapterIDType: "nvidia", "amd", "intel" or leave it undefined
;                                You can define what type of driver version and vendor id's the wrapper should report to
;                                the application; Some games rely on that information so it can be useful for them
;                                Can be defined only for SVGA and Internal3D card types; the others have their own wired
;                                information

; VendorID, DeviceID, SubsystemID, RevisionID:
;                                Can be defined only for SVGA and Internal3D card types
;                                You can overwrite these properties even if a non-default AdapterIDType is defined;
;                                say, you defined an nvidia id type but would like to refine the vendor id

;  DefaultEnumeratedResolutions: you can define what resolutions should be enumerated to the application by default
;                                "all", "classics", "none"

;    ExtraEnumeratedResolutions: you can add extra resolutions (separated by commas, max 16) that will get
;                                enumerated to the application as display adapter supported ones -
;                                can be useful if an app supports rendering at arbitrary resolutions
;                                and you have a particular favorite resolution that are not
;                                enumerated to the application by default
;                                you can either use the compact resolution format here, or
;                                "max", "max@refrate" meaning your desktop resolution with a potential refresh rate, or
;                                "max_4_3", "max_4_3@refrate", "max_16_9", "max_16_9@refrate"
;                                meaning the maximum resolution with the given aspect ratio calculated from
;                                the desktop resolution with the given refresh rate, e.g. "max_4_3@60", "max_16_9"

; EnumeratedResolutionBitdepths: you can filter what bitdepths are included in the resolution enumeration
;                                any subset of {"8", "16", "32"}, or "all"

;               DitheringEffect: "pure32bit", "ordered2x2", "ordered4x4"
;                     Dithering: "disabled", "appdriven", "forceon16bit", "forcealways"
;  DitherOrderedMatrixSizeScale: integer scale value for dither matrix size
;                                1 = normal, 2 = double size, etc.
;                                0 = automatic
;          DepthBuffersBitDepth: internal bit depth of depth/stencil buffers for 3D rendering (32 bit is not recommended)
;                                "appdriven", "forcemin24bit", "force32bit"

;           MaxVSConstRegisters: Max number of vertex shader constant registers (DX8/9 only)
;                                Can be defined only for SVGA and Internal3D card types
;                                Valid values are 256 (default), 512 or 1024

;              MSD3DDeviceNames: if true then original Microsoft D3D device names are exposed
;                                (some applications check for them and they fail)

;   RTTexturesForceScaleAndMSAA: if true then forced resolution scaling and MSAA is
;                                applied also to rendertarget textures
;                                Set it to false for games requiring pixel-precise rendering
;                                but be careful it can EASILY break certain things, not recommended

;         SmoothedDepthSampling: if true then extra smoothing is added to depth textures
;                                when they are sampled

;      DeferredScreenModeSwitch: If true the switching to full screen is deferred after the application initialized
;                                the DirectX device; can be useful for games that don't expect rendering window changes
;                                during initialization and crash

;   PrimarySurfaceBatchedUpdate: If true then direct changes of the primary surface are batched together for presenting them
;                                If false then each change is instantly presented (debug-like mode)

AdapterIDType                       = 
VendorID                            = 
DeviceID                            = 
SubsystemID                         = 
RevisionID                          = 

DefaultEnumeratedResolutions        = all
ExtraEnumeratedResolutions          = 
EnumeratedResolutionBitdepths       = all

DitheringEffect                     = pure32bit
Dithering                           = forcealways
DitherOrderedMatrixSizeScale        = 0
DepthBuffersBitDepth                = appdriven

MaxVSConstRegisters                 = 1024

MSD3DDeviceNames                    = false
RTTexturesForceScaleAndMSAA         = true
SmoothedDepthSampling               = true
DeferredScreenModeSwitch            = false
PrimarySurfaceBatchedUpdate         = true

;--------------------------------------------------------------------------

[Debug]

; This section affects only debug/spec release builds
;
; Info, Warning, Error
;                "Disable"      - disables all messages and debugger break
;                "Enable"       - enables messages and disables debugger break
;                "EnableBreak"  - enables both messages and breaking into debugger
;
; MaxTraceLevel: Maximum level of tracing API calls
;                0 - Disable
;                1 - API Functions and methods
;                2 - Additional trace info for internals
;
; LogToFile: if false or debugger is detected then output goes to the debug output
;            if true and no debugger detected then output goes to 'dgVoodoo.log'
;            (not implemented yet, always the default debug output is used)

Info                                = enable
Warning                             = enable
Error                               = enable
MaxTraceLevel                       = 0

LogToFile                           = false