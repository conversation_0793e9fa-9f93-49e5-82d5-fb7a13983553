{"runtimeTarget": {"name": ".NETCoreApp,Version=v3.1/win-x64", "signature": ""}, "compilationOptions": {}, "targets": {".NETCoreApp,Version=v3.1": {}, ".NETCoreApp,Version=v3.1/win-x64": {"PPeXM64/1.0.0": {"dependencies": {"PPeX": "1.0.0"}, "runtime": {"PPeXM64.dll": {}}}, "Microsoft.NETCore.Platforms/3.1.1": {}, "Nito.AsyncEx.Coordination/5.0.0": {"dependencies": {"Nito.AsyncEx.Tasks": "5.0.0", "Nito.Collections.Deque": "1.0.4", "Nito.Disposables": "2.0.0"}, "runtime": {"lib/netstandard2.0/Nito.AsyncEx.Coordination.dll": {"assemblyVersion": "5.0.0.0", "fileVersion": "5.0.0.0"}}}, "Nito.AsyncEx.Tasks/5.0.0": {"dependencies": {"Nito.Disposables": "2.0.0"}, "runtime": {"lib/netstandard2.0/Nito.AsyncEx.Tasks.dll": {"assemblyVersion": "5.0.0.0", "fileVersion": "5.0.0.0"}}}, "Nito.Collections.Deque/1.0.4": {"runtime": {"lib/netstandard2.0/Nito.Collections.Deque.dll": {"assemblyVersion": "1.0.4.0", "fileVersion": "1.0.4.0"}}}, "Nito.Disposables/2.0.0": {"dependencies": {"System.Collections.Immutable": "1.4.0"}, "runtime": {"lib/netstandard2.0/Nito.Disposables.dll": {"assemblyVersion": "2.0.0.0", "fileVersion": "2.0.0.0"}}}, "System.Collections.Immutable/1.4.0": {}, "System.Text.Encoding.CodePages/4.7.1": {"dependencies": {"Microsoft.NETCore.Platforms": "3.1.1"}, "runtime": {"runtimes/win/lib/netcoreapp2.0/System.Text.Encoding.CodePages.dll": {"assemblyVersion": "4.1.3.0", "fileVersion": "4.700.20.21406"}}}, "PPeX/1.0.0": {"dependencies": {"Nito.AsyncEx.Coordination": "5.0.0", "System.Text.Encoding.CodePages": "4.7.1"}, "runtime": {"PPeX.dll": {}}}}}, "libraries": {"PPeXM64/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "Microsoft.NETCore.Platforms/3.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-RmINcaqiEkawM9C8oxFMN/CZmn1fGKWVsosbSY/8ARUNdHqV47hqhPVbrG3qUqLaRQI5w4HuqFOqrbhoSWcH6w==", "path": "microsoft.netcore.platforms/3.1.1", "hashPath": "microsoft.netcore.platforms.3.1.1.nupkg.sha512"}, "Nito.AsyncEx.Coordination/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-kjauyO8UMo/FGZO/M8TdjXB8ZlBPFOiRN8yakThaGQbYOywazQ0kGZ39SNr2gNNzsTxbZOUudBMYNo+IrtscbA==", "path": "nito.asyncex.coordination/5.0.0", "hashPath": "nito.asyncex.coordination.5.0.0.nupkg.sha512"}, "Nito.AsyncEx.Tasks/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-ZtvotignafOLteP4oEjVcF3k2L8h73QUCaFpVKWbU+EOlW/I+JGkpMoXIl0rlwPcDmR84RxzggLRUNMaWlOosA==", "path": "nito.asyncex.tasks/5.0.0", "hashPath": "nito.asyncex.tasks.5.0.0.nupkg.sha512"}, "Nito.Collections.Deque/1.0.4": {"type": "package", "serviceable": true, "sha512": "sha512-yGDKqCQ61i97MyfEUYG6+ln5vxpx11uA5M9+VV9B7stticbFm19YMI/G9w4AFYVBj5PbPi138P8IovkMFAL0Aw==", "path": "nito.collections.deque/1.0.4", "hashPath": "nito.collections.deque.1.0.4.nupkg.sha512"}, "Nito.Disposables/2.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-ExJl/jTjegSLHGcwnmaYaI5xIlrefAsVdeLft7VLtXI2+W5irihiu36LizWvlaUpzY1/llo+YSh09uSHMu2VFw==", "path": "nito.disposables/2.0.0", "hashPath": "nito.disposables.2.0.0.nupkg.sha512"}, "System.Collections.Immutable/1.4.0": {"type": "package", "serviceable": true, "sha512": "sha512-71hw5RUJRu5+q/geUY69gpXD8Upd12cH+F3MwpXV2zle7Bqqkrmc1JblOTuvUcgmdnUtQvBlV5e1d6RH+H2lvA==", "path": "system.collections.immutable/1.4.0", "hashPath": "system.collections.immutable.1.4.0.nupkg.sha512"}, "System.Text.Encoding.CodePages/4.7.1": {"type": "package", "serviceable": true, "sha512": "sha512-i2fOvznVVgOOTLUz8FgSap/MsR98I4Iaoz99VXcOW/e7Y2OdY42zhYpBYpZyivk5alYY/UsOWAVswhtjxceodA==", "path": "system.text.encoding.codepages/4.7.1", "hashPath": "system.text.encoding.codepages.4.7.1.nupkg.sha512"}, "PPeX/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}}}